# 镜像名称修改任务

## 任务描述
修改 Docker 构建脚本，使用指定的私有仓库地址和版本标签。

## 修改内容

### 1. 修改的文件
- `build.bat` - Docker 构建脚本

### 2. 具体修改
- **原镜像名**: `ngap-engine`
- **新镜像名**: `10.12.135.233/base/ngap-engine:1.0.0-SNAPSHOT`

### 3. 修改详情
```batch
# 修改前
docker build -t ngap-engine .

# 修改后  
docker build -t 10.12.135.233/base/ngap-engine:1.0.0-SNAPSHOT .
```

## 构建和使用

### 构建镜像
```bash
# 运行构建脚本
build.bat

# 或直接使用 Docker 命令
docker build -t 10.12.135.233/base/ngap-engine:1.0.0-SNAPSHOT .
```

### 运行容器
```bash
# 直接运行
docker run -p 5000:5000 10.12.135.233/base/ngap-engine:1.0.0-SNAPSHOT

# 使用 docker-compose（仍使用本地构建）
docker-compose up --build
```

### 推送到私有仓库
```bash
# 推送镜像到私有仓库
docker push 10.12.135.233/base/ngap-engine:1.0.0-SNAPSHOT
```

## 注意事项
1. **私有仓库访问**: 确保 Docker 已配置访问 `10.12.135.233` 私有仓库的权限
2. **网络连接**: 确保能够访问私有仓库地址
3. **认证配置**: 可能需要先登录私有仓库：
   ```bash
   docker login 10.12.135.233
   ```

### 推送脚本
创建了 `push.bat` 脚本，集成了登录和推送功能：

```batch
# 使用推送脚本（包含登录）
push.bat
```

脚本功能：
1. 检查本地镜像是否存在
2. 自动登录私有仓库
3. 推送镜像到仓库
4. 提供详细的错误处理和提示

## 完整工作流程
```bash
# 1. 构建镜像
build.bat

# 2. 推送镜像（包含登录）
push.bat
```

## 执行状态
- [x] 修改 build.bat 构建脚本
- [x] 更新镜像名称和标签
- [x] 创建 push.bat 推送脚本
- [x] 集成登录和推送功能
- [x] 验证修改结果
- [ ] 测试构建和推送（待用户执行）

## 备注
- `docker-compose.yml` 保持使用本地构建方式
- `push.bat` 脚本包含完整的登录和推送流程
- 提供了详细的错误处理和用户提示
