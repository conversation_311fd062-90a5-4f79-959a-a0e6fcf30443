# 使用华为云镜像
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/python:3.11-slim

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# 配置华为云软件源（兼容 Debian 12）
RUN echo "deb https://mirrors.huaweicloud.com/debian/ bookworm main" > /etc/apt/sources.list && \
    echo "deb https://mirrors.huaweicloud.com/debian/ bookworm-updates main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.huaweicloud.com/debian-security/ bookworm-security main" >> /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 使用华为云PyPI镜像
RUN pip config set global.index-url https://mirrors.huaweicloud.com/repository/pypi/simple

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app.py .
COPY voltage_control.py .
COPY storage_voltage_control.py .

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "app.py"]
