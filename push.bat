@echo off

echo Pushing NGAP Engine Docker image to private registry...

set IMAGE_NAME=10.12.135.233/base/ngap-engine:1.0.0-SNAPSHOT

echo.
echo Checking if image exists locally...
docker images %IMAGE_NAME% --format "table {{.Repository}}:{{.Tag}}" | findstr "10.12.135.233/base/ngap-engine"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Image %IMAGE_NAME% not found locally!
    echo Please build the image first using build.bat
    echo.
    pause
    exit /b 1
)

echo.
echo Image found locally. Proceeding with push...
echo.

echo Pushing image: %IMAGE_NAME%
docker push %IMAGE_NAME%

if %ERRORLEVEL% equ 0 (
    echo.
    echo Push successful!
    echo Image %IMAGE_NAME% has been pushed to the registry.
    echo.
) else (
    echo.
    echo Push failed!
    echo.
    echo Possible reasons:
    echo 1. Not logged in to the registry. Try: docker login 10.12.135.233
    echo 2. Network connectivity issues
    echo 3. Registry access permissions
    echo.
)

echo.
pause
